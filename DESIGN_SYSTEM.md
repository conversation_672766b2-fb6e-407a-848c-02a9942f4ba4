# LightQuant Design System

A sophisticated, finance-inspired design system built on modern web technologies with a black/white foundation and strategic electric blue accents.

## Overview

The LightQuant design system embodies trust, sophistication, and innovation through a carefully crafted visual language that prioritizes clarity, elegance, and professional authority. Our design philosophy centers on minimalist elegance with strategic use of color and space.

## Design Principles

### 1. Minimalist Elegance
- **Less is more**: Avoid unnecessary UI decoration and borders
- **Whitespace**: Use generous spacing for breathing room and clarity
- **Clean layouts**: Prefer simple, readable layouts over heavy card usage
- **Purposeful elements**: Every design element should serve a clear function

### 2. Finance-Inspired Sophistication
- **Professional authority**: Serious, trustworthy, and reliable appearance
- **Data-driven clarity**: Clear hierarchy for information presentation
- **Precision**: Exact alignment and consistent spacing throughout
- **Confident presence**: Authoritative visual language that builds trust

### 3. Strategic Color Philosophy
- **Primary theme**: Black/white for main content, navigation, and primary actions
- **Accent sparingly**: Electric blue (#228BFF) only for highlights and special emphasis
- **Avoid color overload**: Resist using multiple bright colors simultaneously
- **Consistent CTAs**: Standardized button colors and interaction patterns

### 4. Layout Philosophy
- **Card usage guidelines**: Use cards only when grouping multiple related pieces of information
- **Simple sections**: Prefer clean text layouts and whitespace over unnecessary containers
- **Border minimalism**: Avoid heavy borders; use subtle separators and alignment instead
- **Breathing room**: Generous spacing between sections and components

## Color Palette

### Primary Colors
```css
--background: #0A0A0A (Black)
--foreground: #FFFFFF (White)
--electric-blue: #228BFF (Primary accent)
```

### Secondary Colors
```css
--secondary: #23272F (Dark gray)
--muted: #181A20 (Subtle gray)
--border: rgba(255, 255, 255, 0.2) (Transparent borders)
```

### Semantic Colors
```css
--success: #10B981 (Green)
--warning: #F59E0B (Amber)
--error: #EF4444 (Red)
```

### Usage Guidelines
- **Electric Blue (#228BFF)**: Use sparingly for CTAs, focus states, and key highlights
- **Black/White**: Primary content and backgrounds
- **Gray Gradients**: Subtle depth and section separation
- **Semantic Colors**: Status indicators and feedback

## Typography

### Font Families
- **Headings**: Space Grotesk (300, 400, 500, 600, 700)
- **Body Text**: Inter (300, 400, 500, 600, 700, 800, 900)
- **Code**: Monospace system fonts

### Type Scale
```css
/* Headings */
.text-6xl: 3.75rem (60px) - Hero titles
.text-5xl: 3rem (48px) - Page titles
.text-4xl: 2.25rem (36px) - Section titles
.text-3xl: 1.875rem (30px) - Subsection titles
.text-2xl: 1.5rem (24px) - Card titles
.text-xl: 1.25rem (20px) - Large text

/* Body */
.text-lg: 1.125rem (18px) - Large body
.text-base: 1rem (16px) - Default body
.text-sm: 0.875rem (14px) - Small text
.text-xs: 0.75rem (12px) - Captions
```

### Usage Guidelines
- Use Space Grotesk for all headings and display text
- Use Inter for body text, navigation, and UI elements
- Maintain consistent line heights (1.2 for headings, 1.6 for body)
- Use font weights purposefully (400 for body, 600+ for emphasis)

## Spacing System

### Scale
```css
0.5: 0.125rem (2px)
1: 0.25rem (4px)
2: 0.5rem (8px)
3: 0.75rem (12px)
4: 1rem (16px)
6: 1.5rem (24px)
8: 2rem (32px)
12: 3rem (48px)
16: 4rem (64px)
20: 5rem (80px)
```

### Usage Guidelines
- Use consistent spacing multiples of 4px
- Maintain generous whitespace for premium feel
- Use larger spacing (16, 20) for section separation
- Use smaller spacing (2, 3, 4) for component internal spacing

## Border Radius

### Scale
```css
.rounded-xl: 0.75rem (12px) - Small components
.rounded-2xl: 1rem (16px) - Cards and buttons (default)
.rounded-3xl: 1.5rem (24px) - Large containers
.rounded-full: 50% - Circular elements
```

### Usage Guidelines
- Default to 2xl (16px) for most components
- Use consistent radius throughout the application
- Avoid mixing different radius values in the same component

## Shadows & Effects

### Shadow Scale
```css
.shadow-lg: Subtle elevation
.shadow-xl: Medium elevation
.shadow-2xl: High elevation
```

### Glassmorphism
```css
backdrop-blur-md: 12px blur
bg-white/10: 10% white opacity
border-white/20: 20% white border opacity
```

### Usage Guidelines
- Use shadows sparingly for depth and hierarchy
- Apply glassmorphism to overlay components
- Combine blur and opacity for frosted glass effect

## Animation & Motion

### Timing Functions
```css
ease-out: Default for most animations
ease-in-out: For reversible animations
ease-in: For exit animations
```

### Duration Scale
```css
150ms: Micro-interactions (hover states)
300ms: Component transitions
500ms: Page transitions
```

### Custom Animations
- **fade-in**: Gentle opacity transition
- **slide-up**: Upward movement with fade
- **scale-in**: Scale from 95% to 100%
- **float**: Gentle vertical floating motion
- **glow**: Electric blue glow effect

### Usage Guidelines
- Keep animations subtle and purposeful
- Use consistent timing across similar interactions
- Prefer transform-based animations for performance
- Respect user preferences for reduced motion

## Component Guidelines

### Buttons
- Use electric blue for primary actions
- Provide clear visual hierarchy
- Include hover and focus states
- Support multiple sizes (sm, md, lg, xl)

### Cards
- Minimal borders, prefer glassmorphism
- Consistent padding and spacing
- Subtle hover effects for interactivity
- Use variants sparingly (default, glass, electric)

### Forms
- Clear labels and helpful error messages
- Consistent input styling
- Electric blue focus states
- Proper spacing between form elements

### Navigation
- Clear hierarchy and active states
- Smooth transitions between states
- Mobile-first responsive design
- Accessible keyboard navigation

## Accessibility

### Color Contrast
- Maintain WCAG AA compliance (4.5:1 minimum)
- Test with color blindness simulators
- Provide alternative indicators beyond color

### Typography
- Minimum 16px font size for body text
- Sufficient line height for readability
- Clear hierarchy through size and weight

### Interactive Elements
- Minimum 44px touch targets
- Clear focus indicators
- Keyboard navigation support
- Screen reader compatibility

## Implementation

### CSS Custom Properties
All design tokens are available as CSS custom properties in `globals.css`.

### Tailwind Configuration
Extended Tailwind configuration includes all custom colors, fonts, and animations.

### Component Library
Reusable components built with shadcn/ui and customized for LightQuant branding.

## Best Practices

### Do's
- Use the design system consistently
- Test across different devices and browsers
- Maintain accessibility standards
- Keep animations subtle and purposeful
- Use electric blue sparingly for maximum impact

### Don'ts
- Don't mix different design patterns
- Don't overuse animations or effects
- Don't ignore accessibility requirements
- Don't use colors outside the defined palette
- Don't create inconsistent spacing patterns

## Resources

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Components](https://ui.shadcn.com/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Space Grotesk Font](https://fonts.google.com/specimen/Space+Grotesk)
- [Inter Font](https://fonts.google.com/specimen/Inter)
