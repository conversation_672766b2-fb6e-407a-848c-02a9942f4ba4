import { BarChart3, Brain, Network, Shield, Zap } from "lucide-react";

const WhyChooseUs = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Intelligence",
      description:
        "Advanced machine learning algorithms continuously optimize trading strategies based on real-time market data and historical performance patterns.",
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description:
        "Bank-grade encryption and API-only integration ensure your funds remain secure while our bots execute trades on your behalf.",
    },
    {
      icon: BarChart3,
      title: "Complete Transparency",
      description:
        "Full access to historical performance data, real-time metrics, and detailed analytics for every trading strategy and bot.",
    },
    {
      icon: Zap,
      title: "Instant Deployment",
      description:
        "Deploy professional trading strategies with a single click. No coding knowledge required, no complex setup processes.",
    },
    {
      icon: Network,
      title: "Multi-Exchange Support",
      description:
        "Seamlessly trade across major exchanges including Binance, Bybit, and OKX with unified portfolio management.",
    },
  ];

  return (
    <section className="py-32 bg-gray-950">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Why Choose LightQuant
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Professional-grade trading infrastructure designed for serious traders who demand
            results, transparency, and reliability.
          </p>
        </div>

        {/* Clean Feature List */}
        <div className="space-y-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            const isEven = index % 2 === 0;

            return (
              <div
                key={index}
                className={`flex flex-col ${isEven ? "lg:flex-row" : "lg:flex-row-reverse"} items-center gap-12 animate-fade-in-up`}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {/* Icon & Title */}
                <div className="flex-1 text-center lg:text-left">
                  <div className="w-20 h-20 bg-white/10 rounded-xl flex items-center justify-center mx-auto lg:mx-0 mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4 font-space-grotesk">
                    {feature.title}
                  </h3>
                </div>

                {/* Description */}
                <div className="flex-1">
                  <p className="text-lg text-gray-400 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
