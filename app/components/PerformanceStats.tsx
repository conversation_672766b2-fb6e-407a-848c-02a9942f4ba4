import { Link, TrendingDown, TrendingUp, Users } from "lucide-react";

const PerformanceStats = () => {
  const stats = [
    {
      icon: TrendingUp,
      value: "+21.5%",
      label: "Average monthly ROI",
      description: "Across all active bots",
    },
    {
      icon: Users,
      value: "10,000+",
      label: "Active traders",
      description: "Worldwide community",
    },
    {
      icon: Link,
      value: "99.9%",
      label: "Platform uptime",
      description: "Enterprise reliability",
    },
    {
      icon: TrendingDown,
      value: "24/7",
      label: "Market monitoring",
      description: "Continuous operation",
    },
  ];

  return (
    <section className="py-32 bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Trusted by Thousands
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Our platform delivers consistent results with institutional-grade infrastructure and
            proven performance metrics.
          </p>
        </div>

        {/* Clean Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="text-center group animate-fade-in-up"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                {/* Icon */}
                <div className="w-16 h-16 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Icon className="w-8 h-8 text-white" />
                </div>

                {/* Value */}
                <div className="text-4xl md:text-5xl font-bold text-white mb-2 group-hover:scale-105 transition-transform duration-300 font-space-grotesk">
                  {stat.value}
                </div>

                {/* Label */}
                <h3 className="text-lg font-semibold text-white mb-1">{stat.label}</h3>

                {/* Description */}
                <p className="text-sm text-gray-400">{stat.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default PerformanceStats;
